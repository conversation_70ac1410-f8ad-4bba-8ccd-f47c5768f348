// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		30EB8D454FB3A605D3BAC790 /* libPods-ModieshaAbstract-ModieshaNetworkExtension.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 64479083B6D65ABBA63246B0 /* libPods-ModieshaAbstract-ModieshaNetworkExtension.a */; };
		B8584B5762AEB019D2B0E4B0 /* libPods-ModieshaAbstract-Modiesha.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 77894A9369CB9F647D71A36F /* libPods-ModieshaAbstract-Modiesha.a */; };
		E42B73A52E0EEE4400068C2B /* NetworkExtension.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E42B73912E0EEE1600068C2B /* NetworkExtension.framework */; };
		E42B73AD2E0EEE4400068C2B /* ModieshaNetworkExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = E42B73A42E0EEE4400068C2B /* ModieshaNetworkExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		E4C47B472E1180B500394481 /* NEVPNStatus+isConnected.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B332E1180B500394481 /* NEVPNStatus+isConnected.swift */; };
		E4C47B482E1180B500394481 /* SharedPreferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3E2E1180B500394481 /* SharedPreferences.swift */; };
		E4C47B492E1180B500394481 /* FilePath.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B292E1180B500394481 /* FilePath.swift */; };
		E4C47B4A2E1180B500394481 /* Profile+RW.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3D2E1180B500394481 /* Profile+RW.swift */; };
		E4C47B4B2E1180B500394481 /* Extension+Iterator.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2F2E1180B500394481 /* Extension+Iterator.swift */; };
		E4C47B4C2E1180B500394481 /* Profile+Transferable.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B432E1180B500394481 /* Profile+Transferable.swift */; };
		E4C47B4D2E1180B500394481 /* Variant.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2A2E1180B500394481 /* Variant.swift */; };
		E4C47B4E2E1180B500394481 /* Bundle+Version.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B282E1180B500394481 /* Bundle+Version.swift */; };
		E4C47B4F2E1180B500394481 /* Profile+Share.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B422E1180B500394481 /* Profile+Share.swift */; };
		E4C47B502E1180B500394481 /* ProfileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3B2E1180B500394481 /* ProfileManager.swift */; };
		E4C47B512E1180B500394481 /* CommandClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B362E1180B500394481 /* CommandClient.swift */; };
		E4C47B522E1180B500394481 /* SystemExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B342E1180B500394481 /* SystemExtension.swift */; };
		E4C47B532E1180B500394481 /* ShadredPreferences+Database.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3F2E1180B500394481 /* ShadredPreferences+Database.swift */; };
		E4C47B542E1180B500394481 /* Profile+Update.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3C2E1180B500394481 /* Profile+Update.swift */; };
		E4C47B552E1180B500394481 /* ExtensionErrors.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2D2E1180B500394481 /* ExtensionErrors.swift */; };
		E4C47B562E1180B500394481 /* ProfileServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B252E1180B500394481 /* ProfileServer.swift */; };
		E4C47B572E1180B500394481 /* Databse.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3A2E1180B500394481 /* Databse.swift */; };
		E4C47B582E1180B500394481 /* Profile.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B392E1180B500394481 /* Profile.swift */; };
		E4C47B592E1180B500394481 /* ExtensionProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2E2E1180B500394481 /* ExtensionProvider.swift */; };
		E4C47B5A2E1180B500394481 /* Extension+RunBlocking.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B302E1180B500394481 /* Extension+RunBlocking.swift */; };
		E4C47B5B2E1180B500394481 /* Library.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B452E1180B500394481 /* Library.swift */; };
		E4C47B5C2E1180B500394481 /* Color+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2B2E1180B500394481 /* Color+Extension.swift */; };
		E4C47B5D2E1180B500394481 /* HTTPClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B312E1180B500394481 /* HTTPClient.swift */; };
		E4C47B5E2E1180B500394481 /* ExtensionProfile.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B322E1180B500394481 /* ExtensionProfile.swift */; };
		E4C47B5F2E1180B500394481 /* Profile+Date.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B402E1180B500394481 /* Profile+Date.swift */; };
		E4C47B602E1180B500394481 /* ExtensionEnvironments.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B372E1180B500394481 /* ExtensionEnvironments.swift */; };
		E4C47B612E1180B500394481 /* ExtensionPlatformInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B352E1180B500394481 /* ExtensionPlatformInterface.swift */; };
		E4C47B622E1180B500394481 /* Profile+Hashable.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B412E1180B500394481 /* Profile+Hashable.swift */; };
		E4C47B632E1180B500394481 /* NWSocket.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B262E1180B500394481 /* NWSocket.swift */; };
		E4C47B812E1180F400394481 /* FilePath.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B292E1180B500394481 /* FilePath.swift */; };
		E4C47B822E1180F400394481 /* Bundle+Version.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B282E1180B500394481 /* Bundle+Version.swift */; };
		E4C47B832E1180F400394481 /* ProfileServer.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B252E1180B500394481 /* ProfileServer.swift */; };
		E4C47B842E1180F400394481 /* ExtensionPlatformInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B352E1180B500394481 /* ExtensionPlatformInterface.swift */; };
		E4C47B852E1180F400394481 /* ExtensionErrors.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2D2E1180B500394481 /* ExtensionErrors.swift */; };
		E4C47B862E1180F400394481 /* ExtensionProfile.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B322E1180B500394481 /* ExtensionProfile.swift */; };
		E4C47B872E1180F400394481 /* ShadredPreferences+Database.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3F2E1180B500394481 /* ShadredPreferences+Database.swift */; };
		E4C47B882E1180F400394481 /* Extension+RunBlocking.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B302E1180B500394481 /* Extension+RunBlocking.swift */; };
		E4C47B892E1180F400394481 /* Profile+Update.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3C2E1180B500394481 /* Profile+Update.swift */; };
		E4C47B8A2E1180F400394481 /* SharedPreferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3E2E1180B500394481 /* SharedPreferences.swift */; };
		E4C47B8B2E1180F400394481 /* Databse.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3A2E1180B500394481 /* Databse.swift */; };
		E4C47B8C2E1180F400394481 /* Variant.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2A2E1180B500394481 /* Variant.swift */; };
		E4C47B8D2E1180F400394481 /* NWSocket.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B262E1180B500394481 /* NWSocket.swift */; };
		E4C47B8E2E1180F400394481 /* SystemExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B342E1180B500394481 /* SystemExtension.swift */; };
		E4C47B8F2E1180F400394481 /* HTTPClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B312E1180B500394481 /* HTTPClient.swift */; };
		E4C47B902E1180F400394481 /* Profile.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B392E1180B500394481 /* Profile.swift */; };
		E4C47B912E1180F400394481 /* Profile+Transferable.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B432E1180B500394481 /* Profile+Transferable.swift */; };
		E4C47B922E1180F400394481 /* Profile+Hashable.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B412E1180B500394481 /* Profile+Hashable.swift */; };
		E4C47B932E1180F400394481 /* ProfileManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3B2E1180B500394481 /* ProfileManager.swift */; };
		E4C47B942E1180F400394481 /* NEVPNStatus+isConnected.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B332E1180B500394481 /* NEVPNStatus+isConnected.swift */; };
		E4C47B952E1180F400394481 /* Color+Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2B2E1180B500394481 /* Color+Extension.swift */; };
		E4C47B962E1180F400394481 /* Profile+Share.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B422E1180B500394481 /* Profile+Share.swift */; };
		E4C47B972E1180F400394481 /* ExtensionEnvironments.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B372E1180B500394481 /* ExtensionEnvironments.swift */; };
		E4C47B982E1180F400394481 /* ExtensionProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2E2E1180B500394481 /* ExtensionProvider.swift */; };
		E4C47B992E1180F400394481 /* Library.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B452E1180B500394481 /* Library.swift */; };
		E4C47B9A2E1180F400394481 /* Profile+Date.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B402E1180B500394481 /* Profile+Date.swift */; };
		E4C47B9B2E1180F400394481 /* Extension+Iterator.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B2F2E1180B500394481 /* Extension+Iterator.swift */; };
		E4C47B9C2E1180F400394481 /* Profile+RW.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B3D2E1180B500394481 /* Profile+RW.swift */; };
		E4C47B9D2E1180F400394481 /* CommandClient.swift in Sources */ = {isa = PBXBuildFile; fileRef = E4C47B362E1180B500394481 /* CommandClient.swift */; };
		E4C47BA02E11845A00394481 /* BinaryCodable in Frameworks */ = {isa = PBXBuildFile; productRef = E4C47B9F2E11845A00394481 /* BinaryCodable */; };
		E4C47BA22E11846E00394481 /* BinaryCodable in Frameworks */ = {isa = PBXBuildFile; productRef = E4C47BA12E11846E00394481 /* BinaryCodable */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		E42B73AB2E0EEE4400068C2B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4B002922E0E80E90097BBC7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E42B73A32E0EEE4400068C2B;
			remoteInfo = ModieshaNetworkExtension;
		};
		E4B002A92E0E80EA0097BBC7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4B002922E0E80E90097BBC7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4B002992E0E80E90097BBC7;
			remoteInfo = Modiesha;
		};
		E4B002B32E0E80EA0097BBC7 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = E4B002922E0E80E90097BBC7 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = E4B002992E0E80E90097BBC7;
			remoteInfo = Modiesha;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		E42B739B2E0EEE1600068C2B /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				E42B73AD2E0EEE4400068C2B /* ModieshaNetworkExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		00F54C2BAD76B8ACE010BFB8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig"; path = "Target Support Files/Pods-ModieshaAbstract-ModieshaNetworkExtension/Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig"; sourceTree = "<group>"; };
		400F6314240A33D117CCC177 /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaAbstract-Modiesha.debug.xcconfig"; path = "Target Support Files/Pods-ModieshaAbstract-Modiesha/Pods-ModieshaAbstract-Modiesha.debug.xcconfig"; sourceTree = "<group>"; };
		64479083B6D65ABBA63246B0 /* libPods-ModieshaAbstract-ModieshaNetworkExtension.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ModieshaAbstract-ModieshaNetworkExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		77894A9369CB9F647D71A36F /* libPods-ModieshaAbstract-Modiesha.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ModieshaAbstract-Modiesha.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		950265E62B5EDEDC1B36FCD0 /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaAbstract-Modiesha.release.xcconfig"; path = "Target Support Files/Pods-ModieshaAbstract-Modiesha/Pods-ModieshaAbstract-Modiesha.release.xcconfig"; sourceTree = "<group>"; };
		B23702733022F55EB89B6357 /* Pods-Modiesha.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Modiesha.debug.xcconfig"; path = "Target Support Files/Pods-Modiesha/Pods-Modiesha.debug.xcconfig"; sourceTree = "<group>"; };
		BBA91DD1C64D196E898DD496 /* Pods-ModieshaNetworkExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaNetworkExtension.release.xcconfig"; path = "Target Support Files/Pods-ModieshaNetworkExtension/Pods-ModieshaNetworkExtension.release.xcconfig"; sourceTree = "<group>"; };
		BD4A77AA76CAD9A2DEA69895 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig"; path = "Target Support Files/Pods-ModieshaAbstract-ModieshaNetworkExtension/Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig"; sourceTree = "<group>"; };
		E42B73912E0EEE1600068C2B /* NetworkExtension.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = NetworkExtension.framework; path = System/Library/Frameworks/NetworkExtension.framework; sourceTree = SDKROOT; };
		E42B73A42E0EEE4400068C2B /* ModieshaNetworkExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ModieshaNetworkExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		E4B0029A2E0E80E90097BBC7 /* Modiesha.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Modiesha.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E4B002A82E0E80EA0097BBC7 /* ModieshaTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ModieshaTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E4B002B22E0E80EA0097BBC7 /* ModieshaUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ModieshaUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		E4C47B252E1180B500394481 /* ProfileServer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileServer.swift; sourceTree = "<group>"; };
		E4C47B262E1180B500394481 /* NWSocket.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NWSocket.swift; sourceTree = "<group>"; };
		E4C47B282E1180B500394481 /* Bundle+Version.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Bundle+Version.swift"; sourceTree = "<group>"; };
		E4C47B292E1180B500394481 /* FilePath.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilePath.swift; sourceTree = "<group>"; };
		E4C47B2A2E1180B500394481 /* Variant.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Variant.swift; sourceTree = "<group>"; };
		E4C47B2B2E1180B500394481 /* Color+Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Color+Extension.swift"; sourceTree = "<group>"; };
		E4C47B2D2E1180B500394481 /* ExtensionErrors.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionErrors.swift; sourceTree = "<group>"; };
		E4C47B2E2E1180B500394481 /* ExtensionProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionProvider.swift; sourceTree = "<group>"; };
		E4C47B2F2E1180B500394481 /* Extension+Iterator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Extension+Iterator.swift"; sourceTree = "<group>"; };
		E4C47B302E1180B500394481 /* Extension+RunBlocking.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Extension+RunBlocking.swift"; sourceTree = "<group>"; };
		E4C47B312E1180B500394481 /* HTTPClient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HTTPClient.swift; sourceTree = "<group>"; };
		E4C47B322E1180B500394481 /* ExtensionProfile.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionProfile.swift; sourceTree = "<group>"; };
		E4C47B332E1180B500394481 /* NEVPNStatus+isConnected.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NEVPNStatus+isConnected.swift"; sourceTree = "<group>"; };
		E4C47B342E1180B500394481 /* SystemExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SystemExtension.swift; sourceTree = "<group>"; };
		E4C47B352E1180B500394481 /* ExtensionPlatformInterface.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionPlatformInterface.swift; sourceTree = "<group>"; };
		E4C47B362E1180B500394481 /* CommandClient.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommandClient.swift; sourceTree = "<group>"; };
		E4C47B372E1180B500394481 /* ExtensionEnvironments.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExtensionEnvironments.swift; sourceTree = "<group>"; };
		E4C47B392E1180B500394481 /* Profile.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Profile.swift; sourceTree = "<group>"; };
		E4C47B3A2E1180B500394481 /* Databse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Databse.swift; sourceTree = "<group>"; };
		E4C47B3B2E1180B500394481 /* ProfileManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProfileManager.swift; sourceTree = "<group>"; };
		E4C47B3C2E1180B500394481 /* Profile+Update.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+Update.swift"; sourceTree = "<group>"; };
		E4C47B3D2E1180B500394481 /* Profile+RW.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+RW.swift"; sourceTree = "<group>"; };
		E4C47B3E2E1180B500394481 /* SharedPreferences.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedPreferences.swift; sourceTree = "<group>"; };
		E4C47B3F2E1180B500394481 /* ShadredPreferences+Database.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "ShadredPreferences+Database.swift"; sourceTree = "<group>"; };
		E4C47B402E1180B500394481 /* Profile+Date.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+Date.swift"; sourceTree = "<group>"; };
		E4C47B412E1180B500394481 /* Profile+Hashable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+Hashable.swift"; sourceTree = "<group>"; };
		E4C47B422E1180B500394481 /* Profile+Share.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+Share.swift"; sourceTree = "<group>"; };
		E4C47B432E1180B500394481 /* Profile+Transferable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Profile+Transferable.swift"; sourceTree = "<group>"; };
		E4C47B452E1180B500394481 /* Library.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Library.swift; sourceTree = "<group>"; };
		E4FBAB58A3A6240A12FBC8F0 /* Pods-Modiesha.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Modiesha.release.xcconfig"; path = "Target Support Files/Pods-Modiesha/Pods-Modiesha.release.xcconfig"; sourceTree = "<group>"; };
		EE6ED21D56CD983789649972 /* Pods-ModieshaNetworkExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ModieshaNetworkExtension.debug.xcconfig"; path = "Target Support Files/Pods-ModieshaNetworkExtension/Pods-ModieshaNetworkExtension.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		E42B73AE2E0EEE4400068C2B /* Exceptions for "ModieshaNetworkExtension" folder in "ModieshaNetworkExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = E42B73A32E0EEE4400068C2B /* ModieshaNetworkExtension */;
		};
		E4C47C0C2E11971500394481 /* Exceptions for "Modiesha" folder in "ModieshaNetworkExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Frameworks/Libbox.xcframework,
			);
			target = E42B73A32E0EEE4400068C2B /* ModieshaNetworkExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		E42B73A62E0EEE4400068C2B /* ModieshaNetworkExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E42B73AE2E0EEE4400068C2B /* Exceptions for "ModieshaNetworkExtension" folder in "ModieshaNetworkExtension" target */,
			);
			path = ModieshaNetworkExtension;
			sourceTree = "<group>";
		};
		E4B0029C2E0E80E90097BBC7 /* Modiesha */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				E4C47C0C2E11971500394481 /* Exceptions for "Modiesha" folder in "ModieshaNetworkExtension" target */,
			);
			path = Modiesha;
			sourceTree = "<group>";
		};
		E4B002AB2E0E80EA0097BBC7 /* ModieshaTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ModieshaTests;
			sourceTree = "<group>";
		};
		E4B002B52E0E80EA0097BBC7 /* ModieshaUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ModieshaUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		E42B73A12E0EEE4400068C2B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E4C47BA22E11846E00394481 /* BinaryCodable in Frameworks */,
				E42B73A52E0EEE4400068C2B /* NetworkExtension.framework in Frameworks */,
				30EB8D454FB3A605D3BAC790 /* libPods-ModieshaAbstract-ModieshaNetworkExtension.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002972E0E80E90097BBC7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				E4C47BA02E11845A00394481 /* BinaryCodable in Frameworks */,
				B8584B5762AEB019D2B0E4B0 /* libPods-ModieshaAbstract-Modiesha.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002A52E0E80EA0097BBC7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002AF2E0E80EA0097BBC7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E42B73902E0EEE1600068C2B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E42B73912E0EEE1600068C2B /* NetworkExtension.framework */,
				77894A9369CB9F647D71A36F /* libPods-ModieshaAbstract-Modiesha.a */,
				64479083B6D65ABBA63246B0 /* libPods-ModieshaAbstract-ModieshaNetworkExtension.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		E4B002912E0E80E90097BBC7 = {
			isa = PBXGroup;
			children = (
				E4C47B462E1180B500394481 /* Library */,
				E4B0029C2E0E80E90097BBC7 /* Modiesha */,
				E42B73A62E0EEE4400068C2B /* ModieshaNetworkExtension */,
				E4B002AB2E0E80EA0097BBC7 /* ModieshaTests */,
				E4B002B52E0E80EA0097BBC7 /* ModieshaUITests */,
				E42B73902E0EEE1600068C2B /* Frameworks */,
				E4B0029B2E0E80E90097BBC7 /* Products */,
				E78FAD40BC5517581ED05A75 /* Pods */,
			);
			sourceTree = "<group>";
		};
		E4B0029B2E0E80E90097BBC7 /* Products */ = {
			isa = PBXGroup;
			children = (
				E4B0029A2E0E80E90097BBC7 /* Modiesha.app */,
				E4B002A82E0E80EA0097BBC7 /* ModieshaTests.xctest */,
				E4B002B22E0E80EA0097BBC7 /* ModieshaUITests.xctest */,
				E42B73A42E0EEE4400068C2B /* ModieshaNetworkExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E4C47B272E1180B500394481 /* Discovery */ = {
			isa = PBXGroup;
			children = (
				E4C47B252E1180B500394481 /* ProfileServer.swift */,
				E4C47B262E1180B500394481 /* NWSocket.swift */,
			);
			path = Discovery;
			sourceTree = "<group>";
		};
		E4C47B2C2E1180B500394481 /* Shared */ = {
			isa = PBXGroup;
			children = (
				E4C47B282E1180B500394481 /* Bundle+Version.swift */,
				E4C47B292E1180B500394481 /* FilePath.swift */,
				E4C47B2A2E1180B500394481 /* Variant.swift */,
				E4C47B2B2E1180B500394481 /* Color+Extension.swift */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		E4C47B382E1180B500394481 /* Network */ = {
			isa = PBXGroup;
			children = (
				E4C47B2D2E1180B500394481 /* ExtensionErrors.swift */,
				E4C47B2E2E1180B500394481 /* ExtensionProvider.swift */,
				E4C47B2F2E1180B500394481 /* Extension+Iterator.swift */,
				E4C47B302E1180B500394481 /* Extension+RunBlocking.swift */,
				E4C47B312E1180B500394481 /* HTTPClient.swift */,
				E4C47B322E1180B500394481 /* ExtensionProfile.swift */,
				E4C47B332E1180B500394481 /* NEVPNStatus+isConnected.swift */,
				E4C47B342E1180B500394481 /* SystemExtension.swift */,
				E4C47B352E1180B500394481 /* ExtensionPlatformInterface.swift */,
				E4C47B362E1180B500394481 /* CommandClient.swift */,
				E4C47B372E1180B500394481 /* ExtensionEnvironments.swift */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		E4C47B442E1180B500394481 /* Database */ = {
			isa = PBXGroup;
			children = (
				E4C47B392E1180B500394481 /* Profile.swift */,
				E4C47B3A2E1180B500394481 /* Databse.swift */,
				E4C47B3B2E1180B500394481 /* ProfileManager.swift */,
				E4C47B3C2E1180B500394481 /* Profile+Update.swift */,
				E4C47B3D2E1180B500394481 /* Profile+RW.swift */,
				E4C47B3E2E1180B500394481 /* SharedPreferences.swift */,
				E4C47B3F2E1180B500394481 /* ShadredPreferences+Database.swift */,
				E4C47B402E1180B500394481 /* Profile+Date.swift */,
				E4C47B412E1180B500394481 /* Profile+Hashable.swift */,
				E4C47B422E1180B500394481 /* Profile+Share.swift */,
				E4C47B432E1180B500394481 /* Profile+Transferable.swift */,
			);
			path = Database;
			sourceTree = "<group>";
		};
		E4C47B462E1180B500394481 /* Library */ = {
			isa = PBXGroup;
			children = (
				E4C47B272E1180B500394481 /* Discovery */,
				E4C47B2C2E1180B500394481 /* Shared */,
				E4C47B382E1180B500394481 /* Network */,
				E4C47B442E1180B500394481 /* Database */,
				E4C47B452E1180B500394481 /* Library.swift */,
			);
			path = Library;
			sourceTree = "<group>";
		};
		E78FAD40BC5517581ED05A75 /* Pods */ = {
			isa = PBXGroup;
			children = (
				B23702733022F55EB89B6357 /* Pods-Modiesha.debug.xcconfig */,
				E4FBAB58A3A6240A12FBC8F0 /* Pods-Modiesha.release.xcconfig */,
				EE6ED21D56CD983789649972 /* Pods-ModieshaNetworkExtension.debug.xcconfig */,
				BBA91DD1C64D196E898DD496 /* Pods-ModieshaNetworkExtension.release.xcconfig */,
				400F6314240A33D117CCC177 /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */,
				950265E62B5EDEDC1B36FCD0 /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */,
				BD4A77AA76CAD9A2DEA69895 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */,
				00F54C2BAD76B8ACE010BFB8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E42B73A32E0EEE4400068C2B /* ModieshaNetworkExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E42B73AF2E0EEE4400068C2B /* Build configuration list for PBXNativeTarget "ModieshaNetworkExtension" */;
			buildPhases = (
				F2C9E3F4E6FC42894B31D4FF /* [CP] Check Pods Manifest.lock */,
				E42B73A02E0EEE4400068C2B /* Sources */,
				E42B73A12E0EEE4400068C2B /* Frameworks */,
				E42B73A22E0EEE4400068C2B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				E42B73A62E0EEE4400068C2B /* ModieshaNetworkExtension */,
			);
			name = ModieshaNetworkExtension;
			packageProductDependencies = (
				E4C47BA12E11846E00394481 /* BinaryCodable */,
			);
			productName = ModieshaNetworkExtension;
			productReference = E42B73A42E0EEE4400068C2B /* ModieshaNetworkExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		E4B002992E0E80E90097BBC7 /* Modiesha */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4B002BC2E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "Modiesha" */;
			buildPhases = (
				A9CCC91647BA1746A7A99525 /* [CP] Check Pods Manifest.lock */,
				E4B002962E0E80E90097BBC7 /* Sources */,
				E4B002972E0E80E90097BBC7 /* Frameworks */,
				E4B002982E0E80E90097BBC7 /* Resources */,
				E42B739B2E0EEE1600068C2B /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				E42B73AC2E0EEE4400068C2B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4B0029C2E0E80E90097BBC7 /* Modiesha */,
			);
			name = Modiesha;
			packageProductDependencies = (
				E4C47B9F2E11845A00394481 /* BinaryCodable */,
			);
			productName = Modiesha;
			productReference = E4B0029A2E0E80E90097BBC7 /* Modiesha.app */;
			productType = "com.apple.product-type.application";
		};
		E4B002A72E0E80EA0097BBC7 /* ModieshaTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4B002BF2E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "ModieshaTests" */;
			buildPhases = (
				E4B002A42E0E80EA0097BBC7 /* Sources */,
				E4B002A52E0E80EA0097BBC7 /* Frameworks */,
				E4B002A62E0E80EA0097BBC7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E4B002AA2E0E80EA0097BBC7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4B002AB2E0E80EA0097BBC7 /* ModieshaTests */,
			);
			name = ModieshaTests;
			productName = ModieshaTests;
			productReference = E4B002A82E0E80EA0097BBC7 /* ModieshaTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		E4B002B12E0E80EA0097BBC7 /* ModieshaUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E4B002C22E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "ModieshaUITests" */;
			buildPhases = (
				E4B002AE2E0E80EA0097BBC7 /* Sources */,
				E4B002AF2E0E80EA0097BBC7 /* Frameworks */,
				E4B002B02E0E80EA0097BBC7 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				E4B002B42E0E80EA0097BBC7 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				E4B002B52E0E80EA0097BBC7 /* ModieshaUITests */,
			);
			name = ModieshaUITests;
			productName = ModieshaUITests;
			productReference = E4B002B22E0E80EA0097BBC7 /* ModieshaUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E4B002922E0E80E90097BBC7 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					E42B73A32E0EEE4400068C2B = {
						CreatedOnToolsVersion = 16.3;
					};
					E4B002992E0E80E90097BBC7 = {
						CreatedOnToolsVersion = 16.3;
					};
					E4B002A72E0E80EA0097BBC7 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = E4B002992E0E80E90097BBC7;
					};
					E4B002B12E0E80EA0097BBC7 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = E4B002992E0E80E90097BBC7;
					};
				};
			};
			buildConfigurationList = E4B002952E0E80E90097BBC7 /* Build configuration list for PBXProject "Modiesha" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E4B002912E0E80E90097BBC7;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				E4C47B9E2E1181FA00394481 /* XCRemoteSwiftPackageReference "BinaryCodable" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = E4B0029B2E0E80E90097BBC7 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E4B002992E0E80E90097BBC7 /* Modiesha */,
				E42B73A32E0EEE4400068C2B /* ModieshaNetworkExtension */,
				E4B002A72E0E80EA0097BBC7 /* ModieshaTests */,
				E4B002B12E0E80EA0097BBC7 /* ModieshaUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E42B73A22E0EEE4400068C2B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002982E0E80E90097BBC7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002A62E0E80EA0097BBC7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002B02E0E80EA0097BBC7 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A9CCC91647BA1746A7A99525 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ModieshaAbstract-Modiesha-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F2C9E3F4E6FC42894B31D4FF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ModieshaAbstract-ModieshaNetworkExtension-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E42B73A02E0EEE4400068C2B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E4C47B812E1180F400394481 /* FilePath.swift in Sources */,
				E4C47B822E1180F400394481 /* Bundle+Version.swift in Sources */,
				E4C47B832E1180F400394481 /* ProfileServer.swift in Sources */,
				E4C47B842E1180F400394481 /* ExtensionPlatformInterface.swift in Sources */,
				E4C47B852E1180F400394481 /* ExtensionErrors.swift in Sources */,
				E4C47B862E1180F400394481 /* ExtensionProfile.swift in Sources */,
				E4C47B872E1180F400394481 /* ShadredPreferences+Database.swift in Sources */,
				E4C47B882E1180F400394481 /* Extension+RunBlocking.swift in Sources */,
				E4C47B892E1180F400394481 /* Profile+Update.swift in Sources */,
				E4C47B8A2E1180F400394481 /* SharedPreferences.swift in Sources */,
				E4C47B8B2E1180F400394481 /* Databse.swift in Sources */,
				E4C47B8C2E1180F400394481 /* Variant.swift in Sources */,
				E4C47B8D2E1180F400394481 /* NWSocket.swift in Sources */,
				E4C47B8E2E1180F400394481 /* SystemExtension.swift in Sources */,
				E4C47B8F2E1180F400394481 /* HTTPClient.swift in Sources */,
				E4C47B902E1180F400394481 /* Profile.swift in Sources */,
				E4C47B912E1180F400394481 /* Profile+Transferable.swift in Sources */,
				E4C47B922E1180F400394481 /* Profile+Hashable.swift in Sources */,
				E4C47B932E1180F400394481 /* ProfileManager.swift in Sources */,
				E4C47B942E1180F400394481 /* NEVPNStatus+isConnected.swift in Sources */,
				E4C47B952E1180F400394481 /* Color+Extension.swift in Sources */,
				E4C47B962E1180F400394481 /* Profile+Share.swift in Sources */,
				E4C47B972E1180F400394481 /* ExtensionEnvironments.swift in Sources */,
				E4C47B982E1180F400394481 /* ExtensionProvider.swift in Sources */,
				E4C47B992E1180F400394481 /* Library.swift in Sources */,
				E4C47B9A2E1180F400394481 /* Profile+Date.swift in Sources */,
				E4C47B9B2E1180F400394481 /* Extension+Iterator.swift in Sources */,
				E4C47B9C2E1180F400394481 /* Profile+RW.swift in Sources */,
				E4C47B9D2E1180F400394481 /* CommandClient.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002962E0E80E90097BBC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				E4C47B472E1180B500394481 /* NEVPNStatus+isConnected.swift in Sources */,
				E4C47B482E1180B500394481 /* SharedPreferences.swift in Sources */,
				E4C47B492E1180B500394481 /* FilePath.swift in Sources */,
				E4C47B4A2E1180B500394481 /* Profile+RW.swift in Sources */,
				E4C47B4B2E1180B500394481 /* Extension+Iterator.swift in Sources */,
				E4C47B4C2E1180B500394481 /* Profile+Transferable.swift in Sources */,
				E4C47B4D2E1180B500394481 /* Variant.swift in Sources */,
				E4C47B4E2E1180B500394481 /* Bundle+Version.swift in Sources */,
				E4C47B4F2E1180B500394481 /* Profile+Share.swift in Sources */,
				E4C47B502E1180B500394481 /* ProfileManager.swift in Sources */,
				E4C47B512E1180B500394481 /* CommandClient.swift in Sources */,
				E4C47B522E1180B500394481 /* SystemExtension.swift in Sources */,
				E4C47B532E1180B500394481 /* ShadredPreferences+Database.swift in Sources */,
				E4C47B542E1180B500394481 /* Profile+Update.swift in Sources */,
				E4C47B552E1180B500394481 /* ExtensionErrors.swift in Sources */,
				E4C47B562E1180B500394481 /* ProfileServer.swift in Sources */,
				E4C47B572E1180B500394481 /* Databse.swift in Sources */,
				E4C47B582E1180B500394481 /* Profile.swift in Sources */,
				E4C47B592E1180B500394481 /* ExtensionProvider.swift in Sources */,
				E4C47B5A2E1180B500394481 /* Extension+RunBlocking.swift in Sources */,
				E4C47B5B2E1180B500394481 /* Library.swift in Sources */,
				E4C47B5C2E1180B500394481 /* Color+Extension.swift in Sources */,
				E4C47B5D2E1180B500394481 /* HTTPClient.swift in Sources */,
				E4C47B5E2E1180B500394481 /* ExtensionProfile.swift in Sources */,
				E4C47B5F2E1180B500394481 /* Profile+Date.swift in Sources */,
				E4C47B602E1180B500394481 /* ExtensionEnvironments.swift in Sources */,
				E4C47B612E1180B500394481 /* ExtensionPlatformInterface.swift in Sources */,
				E4C47B622E1180B500394481 /* Profile+Hashable.swift in Sources */,
				E4C47B632E1180B500394481 /* NWSocket.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002A42E0E80EA0097BBC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		E4B002AE2E0E80EA0097BBC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		E42B73AC2E0EEE4400068C2B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E42B73A32E0EEE4400068C2B /* ModieshaNetworkExtension */;
			targetProxy = E42B73AB2E0EEE4400068C2B /* PBXContainerItemProxy */;
		};
		E4B002AA2E0E80EA0097BBC7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4B002992E0E80E90097BBC7 /* Modiesha */;
			targetProxy = E4B002A92E0E80EA0097BBC7 /* PBXContainerItemProxy */;
		};
		E4B002B42E0E80EA0097BBC7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = E4B002992E0E80E90097BBC7 /* Modiesha */;
			targetProxy = E4B002B32E0E80EA0097BBC7 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		E42B73B02E0EEE4400068C2B /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BD4A77AA76CAD9A2DEA69895 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ModieshaNetworkExtension/ModieshaNetworkExtension.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ModieshaNetworkExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ModieshaNetworkExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.networkextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Modiesha Network Extension";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		E42B73B12E0EEE4400068C2B /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 00F54C2BAD76B8ACE010BFB8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ModieshaNetworkExtension/ModieshaNetworkExtension.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ModieshaNetworkExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ModieshaNetworkExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.networkextension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Modiesha Network Extension";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		E4B002BA2E0E80EA0097BBC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E4B002BB2E0E80EA0097BBC7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = XS26CEDQLL;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		E4B002BD2E0E80EA0097BBC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 400F6314240A33D117CCC177 /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Modiesha/Modiesha.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/macos-arm64_x86_64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/tvos-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/ios-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/ios-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/tvos-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/macos-arm64_x86_64",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Modiesha Dev";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		E4B002BE2E0E80EA0097BBC7 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 950265E62B5EDEDC1B36FCD0 /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Modiesha/Modiesha.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = XS26CEDQLL;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/macos-arm64_x86_64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/tvos-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.disabled/ios-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/ios-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/tvos-arm64",
					"$(PROJECT_DIR)/Modiesha/Frameworks/Libbox.xcframework.temp/macos-arm64_x86_64",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Modiesha Dev";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		E4B002C02E0E80EA0097BBC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.ModieshaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Modiesha.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Modiesha";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		E4B002C12E0E80EA0097BBC7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.ModieshaTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Modiesha.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Modiesha";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		E4B002C32E0E80EA0097BBC7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.ModieshaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Modiesha;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		E4B002C42E0E80EA0097BBC7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = XS26CEDQLL;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = cn.seungyu.modiesha.ModieshaUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Modiesha;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E42B73AF2E0EEE4400068C2B /* Build configuration list for PBXNativeTarget "ModieshaNetworkExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E42B73B02E0EEE4400068C2B /* Debug */,
				E42B73B12E0EEE4400068C2B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4B002952E0E80E90097BBC7 /* Build configuration list for PBXProject "Modiesha" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4B002BA2E0E80EA0097BBC7 /* Debug */,
				E4B002BB2E0E80EA0097BBC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4B002BC2E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "Modiesha" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4B002BD2E0E80EA0097BBC7 /* Debug */,
				E4B002BE2E0E80EA0097BBC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4B002BF2E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "ModieshaTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4B002C02E0E80EA0097BBC7 /* Debug */,
				E4B002C12E0E80EA0097BBC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E4B002C22E0E80EA0097BBC7 /* Build configuration list for PBXNativeTarget "ModieshaUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E4B002C32E0E80EA0097BBC7 /* Debug */,
				E4B002C42E0E80EA0097BBC7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		E4C47B9E2E1181FA00394481 /* XCRemoteSwiftPackageReference "BinaryCodable" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/christophhagen/BinaryCodable";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.0.3;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		E4C47B9F2E11845A00394481 /* BinaryCodable */ = {
			isa = XCSwiftPackageProductDependency;
			package = E4C47B9E2E1181FA00394481 /* XCRemoteSwiftPackageReference "BinaryCodable" */;
			productName = BinaryCodable;
		};
		E4C47BA12E11846E00394481 /* BinaryCodable */ = {
			isa = XCSwiftPackageProductDependency;
			package = E4C47B9E2E1181FA00394481 /* XCRemoteSwiftPackageReference "BinaryCodable" */;
			productName = BinaryCodable;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = E4B002922E0E80E90097BBC7 /* Project object */;
}
