import Foundation
import SwiftUI

/// Manages local storage and selection of configuration files
@MainActor
class ConfigFileManager: ObservableObject {
    
    static var shared = ConfigFileManager()
    
    @Published var savedConfigs: [SavedConfig] = []
    @Published var selectedConfig: SavedConfig?
    
    private let documentsDirectory: URL
    private let configsDirectory: URL
    
    init() {
        // Get documents directory
        documentsDirectory = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: "group.cn.seungyu.modieshagroup")!
        configsDirectory = documentsDirectory.appendingPathComponent("Configs")
        
        // Create configs directory if it doesn't exist
        createConfigsDirectoryIfNeeded()
        
        // Load saved configurations
        loadSavedConfigs()
    }
    
    // MARK: - Public Methods
    
    /// Save a configuration file locally
    func saveConfig(from sourceURL: URL, name: String? = nil) async throws -> SavedConfig {
        let configName = name ?? sourceURL.deletingPathExtension().lastPathComponent
        let fileName = "\(configName)_\(Date().timeIntervalSince1970).yaml"
        let destinationURL = configsDirectory.appendingPathComponent(fileName)
        
        // Read and validate configuration
        let configData = try Data(contentsOf: sourceURL)
        try validateConfigurationData(configData)
        
        // Save to local directory
        try configData.write(to: destinationURL)
        
        // Create saved config object
        let savedConfig = SavedConfig(
            id: UUID(),
            name: configName,
            fileName: fileName,
            filePath: destinationURL,
            dateAdded: Date(),
            fileSize: configData.count,
            isSelected: savedConfigs.isEmpty // Auto-select if first config
        )
        
        // Add to saved configs
        savedConfigs.append(savedConfig)
        
        // Auto-select if it's the first config
        if selectedConfig == nil {
            selectedConfig = savedConfig
            savedConfig.isSelected = true
        }
        
        // Save configs list
        saveSavedConfigsList()
        
        print("ConfigFileManager: Saved config '\(configName)' to \(destinationURL.lastPathComponent)")
        return savedConfig
    }
    
    /// Select a configuration for use
    func selectConfig(_ config: SavedConfig) {
        // Deselect previous
        selectedConfig?.isSelected = false
        
        // Select new config
        selectedConfig = config
        config.isSelected = true
        
        // Update saved configs list
        saveSavedConfigsList()
        
        print("ConfigFileManager: Selected config '\(config.name)'")
    }
    
    /// Delete a configuration file
    func deleteConfig(_ config: SavedConfig) throws {
        // Remove file from disk
        if FileManager.default.fileExists(atPath: config.filePath.path) {
            try FileManager.default.removeItem(at: config.filePath)
        }

        // Remove from saved configs
        savedConfigs.removeAll { $0.id == config.id }

        // If this was the selected config, select another one
        if selectedConfig?.id == config.id {
            selectedConfig = savedConfigs.first
            selectedConfig?.isSelected = true
        }

        // Save updated list
        saveSavedConfigsList()

        print("ConfigFileManager: Deleted config '\(config.name)'")
    }

    /// Test method to validate file access for debugging
    func testSelectedConfigAccess() -> String {
        guard let selectedConfig = selectedConfig else {
            return "❌ No config selected"
        }

        var result = "Testing config: '\(selectedConfig.name)'\n"
        result += "File path: \(selectedConfig.filePath.path)\n"
        result += "File name: \(selectedConfig.fileName)\n"

        // Test file existence
        let fileExists = FileManager.default.fileExists(atPath: selectedConfig.filePath.path)
        result += "File exists: \(fileExists ? "✅" : "❌")\n"

        if fileExists {
            // Test file access
            let hasAccess = selectedConfig.filePath.startAccessingSecurityScopedResource()
            defer {
                if hasAccess {
                    selectedConfig.filePath.stopAccessingSecurityScopedResource()
                }
            }

            do {
                let data = try Data(contentsOf: selectedConfig.filePath)
                result += "File readable: ✅ (\(data.count) bytes)\n"
            } catch {
                result += "File readable: ❌ (\(error.localizedDescription))\n"
            }
        } else {
            // List parent directory
            let parentDir = selectedConfig.filePath.deletingLastPathComponent()
            if let contents = try? FileManager.default.contentsOfDirectory(atPath: parentDir.path) {
                result += "Parent directory contents: \(contents)\n"
            }
        }

        return result
    }
    
    /// Get configuration data for the selected config
    func getSelectedConfigData() throws -> Data? {
        guard let selectedConfig = selectedConfig else {
            print("ConfigFileManager: No config selected")
            return nil
        }

        print("ConfigFileManager: Loading config data from: \(selectedConfig.filePath.path)")
        print("ConfigFileManager: File URL: \(selectedConfig.filePath)")
        print("ConfigFileManager: File name: \(selectedConfig.fileName)")

        // Check if file exists
        guard FileManager.default.fileExists(atPath: selectedConfig.filePath.path) else {
            print("ConfigFileManager: Config file does not exist at path: \(selectedConfig.filePath.path)")

            // Try to list directory contents for debugging
            let parentDir = selectedConfig.filePath.deletingLastPathComponent()
            if let contents = try? FileManager.default.contentsOfDirectory(atPath: parentDir.path) {
                print("ConfigFileManager: Parent directory contents: \(contents)")
            }

            throw ConfigError.fileNotFound
        }

        // Handle security-scoped resource access for imported files
        let hasAccess = selectedConfig.filePath.startAccessingSecurityScopedResource()
        defer {
            if hasAccess {
                selectedConfig.filePath.stopAccessingSecurityScopedResource()
            }
        }

        do {
            let data = try Data(contentsOf: selectedConfig.filePath)
            print("ConfigFileManager: Successfully loaded \(data.count) bytes from config file")
            return data
        } catch {
            print("ConfigFileManager: Failed to read config file: \(error.localizedDescription)")
            print("ConfigFileManager: Error details: \(error)")
            throw error
        }
    }
    
    /// Import multiple configuration files
    func importConfigs(from urls: [URL]) async throws -> [SavedConfig] {
        var importedConfigs: [SavedConfig] = []

        print("ConfigFileManager: Starting import of \(urls.count) files")

        for url in urls {
            do {
                print("ConfigFileManager: Importing \(url.lastPathComponent)...")

                // Start accessing security-scoped resource
                let hasAccess = url.startAccessingSecurityScopedResource()
                defer {
                    if hasAccess {
                        url.stopAccessingSecurityScopedResource()
                    }
                }

                let savedConfig = try await saveConfig(from: url)
                importedConfigs.append(savedConfig)
                print("ConfigFileManager: Successfully imported \(url.lastPathComponent)")
            } catch {
                print("ConfigFileManager: Failed to import \(url.lastPathComponent): \(error.localizedDescription)")
                // Continue with other files - don't stop the entire import process
            }
        }

        print("ConfigFileManager: Import completed - \(importedConfigs.count) of \(urls.count) files imported")
        return importedConfigs
    }
    
    // MARK: - Private Methods
    
    private func createConfigsDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: configsDirectory.path) {
            try? FileManager.default.createDirectory(at: configsDirectory, withIntermediateDirectories: true)
        }
    }
    
    private func validateConfigurationData(_ data: Data) throws {
        // More flexible validation - accept various configuration formats

        // Check if file is not empty
        guard !data.isEmpty else {
            print("ConfigFileManager: Configuration file is empty")
            throw ConfigError.invalidFormat
        }

        // Try to convert to string to ensure it's readable text
        guard let configString = String(data: data, encoding: .utf8), !configString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            print("ConfigFileManager: Configuration file is not valid UTF-8 text")
            throw ConfigError.invalidFormat
        }

        // Check for common configuration file indicators
        let lowercaseConfig = configString.lowercased()
        let hasValidIndicators = lowercaseConfig.contains("server") ||
                                lowercaseConfig.contains("proxy") ||
                                lowercaseConfig.contains("outbound") ||
                                lowercaseConfig.contains("inbound") ||
                                lowercaseConfig.contains("shadowsocks") ||
                                lowercaseConfig.contains("vmess") ||
                                lowercaseConfig.contains("trojan") ||
                                lowercaseConfig.contains("hysteria") ||
                                lowercaseConfig.contains("vless") ||
                                lowercaseConfig.contains("{") || // JSON format
                                lowercaseConfig.contains("port:") || // YAML format
                                lowercaseConfig.contains("host:")

        guard hasValidIndicators else {
            print("ConfigFileManager: Configuration file doesn't contain expected proxy configuration keywords")
            throw ConfigError.invalidFormat
        }

        print("ConfigFileManager: Configuration validation passed - \(data.count) bytes, contains proxy configuration")
    }

    func loadSavedConfigs() {
        let configsListURL = documentsDirectory.appendingPathComponent("saved_configs.json")

        guard FileManager.default.fileExists(atPath: configsListURL.path),
              let data = try? Data(contentsOf: configsListURL),
              let configs = try? JSONDecoder().decode([SavedConfig].self, from: data) else {
            print("ConfigFileManager: No saved configs found or failed to decode")
            return
        }

        print("ConfigFileManager: Found \(configs.count) saved configs, validating file existence...")

        // Filter out configs whose files no longer exist and log details
        savedConfigs = configs.compactMap { config in
            let fileExists = FileManager.default.fileExists(atPath: config.filePath.path)
            if fileExists {
                print("ConfigFileManager: ✅ Config '\(config.name)' file exists at: \(config.filePath.path)")
                return config
            } else {
                print("ConfigFileManager: ❌ Config '\(config.name)' file missing at: \(config.filePath.path)")
                return nil
            }
        }

        // Set selected config
        selectedConfig = savedConfigs.first { $0.isSelected }
        if let selected = selectedConfig {
            print("ConfigFileManager: Selected config: '\(selected.name)'")
        } else if let first = savedConfigs.first {
            // Auto-select first config if none is selected
            selectedConfig = first
            first.isSelected = true
            print("ConfigFileManager: Auto-selected first config: '\(first.name)'")
        }

        print("ConfigFileManager: Loaded \(savedConfigs.count) valid saved configs")
    }
    
    private func saveSavedConfigsList() {
        let configsListURL = documentsDirectory.appendingPathComponent("saved_configs.json")
        
        do {
            let data = try JSONEncoder().encode(savedConfigs)
            try data.write(to: configsListURL)
        } catch {
            print("ConfigFileManager: Failed to save configs list: \(error)")
        }
    }
}

// MARK: - SavedConfig Model

class SavedConfig: ObservableObject, Identifiable, Codable {
    let id: UUID
    let name: String
    let fileName: String
    let filePath: URL
    let dateAdded: Date
    let fileSize: Int
    @Published var isSelected: Bool
    
    init(id: UUID, name: String, fileName: String, filePath: URL, dateAdded: Date, fileSize: Int, isSelected: Bool) {
        self.id = id
        self.name = name
        self.fileName = fileName
        self.filePath = filePath
        self.dateAdded = dateAdded
        self.fileSize = fileSize
        self.isSelected = isSelected
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case id, name, fileName, filePath, dateAdded, fileSize, isSelected
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        fileName = try container.decode(String.self, forKey: .fileName)
        filePath = try container.decode(URL.self, forKey: .filePath)
        dateAdded = try container.decode(Date.self, forKey: .dateAdded)
        fileSize = try container.decode(Int.self, forKey: .fileSize)
        isSelected = try container.decode(Bool.self, forKey: .isSelected)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(fileName, forKey: .fileName)
        try container.encode(filePath, forKey: .filePath)
        try container.encode(dateAdded, forKey: .dateAdded)
        try container.encode(fileSize, forKey: .fileSize)
        try container.encode(isSelected, forKey: .isSelected)
    }
    
    // MARK: - Computed Properties
    
    var formattedFileSize: String {
        ByteCountFormatter.string(fromByteCount: Int64(fileSize), countStyle: .file)
    }
    
    var formattedDateAdded: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: dateAdded)
    }
}

// MARK: - Errors

enum ConfigError: Error, LocalizedError {
    case invalidFormat
    case fileNotFound
    case saveFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidFormat:
            return "Invalid configuration file format"
        case .fileNotFound:
            return "Configuration file not found"
        case .saveFailed:
            return "Failed to save configuration file"
        }
    }
}
