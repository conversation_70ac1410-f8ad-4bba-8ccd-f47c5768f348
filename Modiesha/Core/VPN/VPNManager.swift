import Foundation
import NetworkExtension
import Combine

@MainActor
class VPNManager: ObservableObject {
    
    // MARK: - Published Properties
    @Published var connectionStatus: NEVPNStatus = .invalid
    @Published var isConnected: Bool = false
    @Published var lastError: Error?
    @Published var connectionStats: VPNStats = VPNStats()

    // MARK: - Additional Properties (from ProxyManager)
    @Published var bytesReceived: Int64 = 0
    @Published var bytesSent: Int64 = 0
    
    @Published var configManager = ConfigFileManager.shared
    
    // MARK: - Private Properties
    private var vpnManager: NETunnelProviderManager?
    private var statusObserver: NSObjectProtocol?
    private var statsTimer: Timer?
    
    // MARK: - Constants
    private let extensionBundleIdentifier = "cn.seungyu.modiesha.networkextension"
    private let providerBundleIdentifier = "cn.seungyu.modiesha.networkextension"

    // MARK: - Computed Properties

    /// Simplified connection status string for UI display
    var connectionStatusString: String {
        switch connectionStatus {
        case .invalid, .disconnected:
            return "未连接"
        case .connecting, .reasserting:
            return "连接中"
        case .connected:
            return "已连接"
        case .disconnecting:
            return "断开中"
        @unknown default:
            return "未知状态"
        }
    }
    
    // MARK: - Initialization
    init() {
        setupVPNManager()
        startStatusObservation()
    }
    
    deinit {
        // Clean up synchronously to avoid capture issues
        if let observer = statusObserver {
            NotificationCenter.default.removeObserver(observer)
            statusObserver = nil
        }
        statsTimer?.invalidate()
        statsTimer = nil
    }
    
    // MARK: - Public Methods
    
    private func createBasicSingBoxConfig(mode: SimpleProxyMode) async throws -> Data {
        // Create a basic sing-box configuration
        let config: [String: Any] = [
            "log": ["level": "info"],
            "dns": ["servers": [["tag": "default", "address": "*******"]]],
            "inbounds": [[
                "type": "tun",
                "tag": "tun-in",
                "interface_name": "utun0",
                "inet4_address": "**********/30",
                "auto_route": true,
                "strict_route": false,
                "sniff": true
            ]],
            "outbounds": [["type": "direct", "tag": "direct"]],
            "route": ["rules": [["outbound": "direct"]]]
        ]
        return try JSONSerialization.data(withJSONObject: config, options: .prettyPrinted)
    }
    
    func connect() async throws {

        configManager.loadSavedConfigs()
        guard var configuration = try configManager.getSelectedConfigData() else {
            return
        }
        
        configuration = try await createBasicSingBoxConfig(mode: .rule)
        
        guard let manager = vpnManager else {
            print("VPNManager: VPN manager not initialized")
            throw VPNError.managerNotInitialized
        }

        // Check if already connected
        if manager.connection.status == .connected {
            print("VPNManager: Already connected")
            return
        }
        
        print("VPNManager: Starting VPN connection with real Network Extension")

        // Validate SingBox configuration before starting VPN
        try await validateSingBoxConfiguration(configuration)

        // Update configuration
        try await updateConfiguration(configuration)

        // Start VPN connection - this will trigger Network Extension to start SingBox
        do {
            try manager.connection.startVPNTunnel(options: [
                "config": configuration as NSObject
            ])

            print("VPNManager: VPN tunnel start command sent - Network Extension will start SingBox")

            // Start statistics monitoring
            startStatsTimer()

        } catch {
            print("VPNManager: Failed to start VPN tunnel: \(error)")
            lastError = error
            throw VPNError.connectionFailed(error)
        }
    }
    
    func disconnect() async throws {
        guard let manager = vpnManager else {
            throw VPNError.managerNotInitialized
        }
        
        manager.connection.stopVPNTunnel()
        stopStatsTimer()
    }
    
    func sendMessage(_ message: [String: Any]) async throws -> [String: Any]? {
        guard let manager = vpnManager,
              let session = manager.connection as? NETunnelProviderSession else {
            throw VPNError.sessionNotAvailable
        }
        
        let messageData = try JSONSerialization.data(withJSONObject: message)
        
        return try await withCheckedThrowingContinuation { continuation in
            do {
                try session.sendProviderMessage(messageData) { responseData in
                    if let responseData = responseData,
                       let response = try? JSONSerialization.jsonObject(with: responseData) as? [String: Any] {
                        continuation.resume(returning: response)
                    } else {
                        continuation.resume(returning: nil)
                    }
                }
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
    
    func requestStatus() async throws -> VPNStats {
        let response = try await sendMessage(["type": "status"])
        
        if let response = response {
            return VPNStats(
                connected: response["connected"] as? Bool ?? false,
                uptime: response["uptime"] as? TimeInterval ?? 0,
                bytesReceived: response["bytesReceived"] as? UInt64 ?? 0,
                bytesSent: response["bytesSent"] as? UInt64 ?? 0
            )
        }
        
        return VPNStats()
    }

    /// Refresh VPN configuration (replaces ProxyManager.refreshConfiguration)
    func refreshConfiguration() async throws {
        print("VPNManager: Refreshing VPN configuration...")

        guard let manager = vpnManager else {
            throw VPNError.managerNotInitialized
        }

        // Reload from preferences to get latest configuration
        try await manager.loadFromPreferences()

        // Update connection status
        updateConnectionStatus()

        print("VPNManager: Configuration refreshed successfully")
    }

    // MARK: - Private Methods
    
    private func setupVPNManager() {
        Task {
            do {
                let managers = try await NETunnelProviderManager.loadAllFromPreferences()
                
                if let existingManager = managers.first {
                    self.vpnManager = existingManager
                    self.vpnManager?.isEnabled = true
                } else {
                    // Create new manager
                    let manager = NETunnelProviderManager()
                    let protocolConfiguration = NETunnelProviderProtocol()
                    
                    protocolConfiguration.providerBundleIdentifier = providerBundleIdentifier
                    protocolConfiguration.serverAddress = "Modiesha VPN"
                    
                    manager.protocolConfiguration = protocolConfiguration
                    manager.localizedDescription = "Modiesha VPN"
                    manager.isEnabled = true
                    
                    try await manager.saveToPreferences()
                    try await manager.loadFromPreferences()
                    
                    self.vpnManager = manager
                }
                
                // Update initial status
                updateConnectionStatus()
                
            } catch {
                self.lastError = error
                print("VPNManager: Failed to setup VPN manager: \(error)")
            }
        }
    }
    
    private func updateConfiguration(_ configData: Data) async throws {
        guard let manager = vpnManager else {
            throw VPNError.managerNotInitialized
        }
        
        // Update protocol configuration if needed
        if let protocolConfig = manager.protocolConfiguration as? NETunnelProviderProtocol {
            protocolConfig.providerConfiguration = [
                "config": configData
            ]
        }
        
        try await manager.saveToPreferences()
        try await manager.loadFromPreferences()
    }
    
    private func startStatusObservation() {
        statusObserver = NotificationCenter.default.addObserver(
            forName: .NEVPNStatusDidChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.updateConnectionStatus()
            }
        }
    }
    
    private func stopStatusObservation() {
        if let observer = statusObserver {
            NotificationCenter.default.removeObserver(observer)
            statusObserver = nil
        }
    }
    
    private func updateConnectionStatus() {
        guard let manager = vpnManager else { return }
        
        connectionStatus = manager.connection.status
        isConnected = connectionStatus == .connected
        
        print("VPNManager: Status changed to \(connectionStatus)")
    }
    
    private func startStatsTimer() {
        stopStatsTimer()
        
        statsTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                do {
                    let stats = try await self?.requestStatus() ?? VPNStats()
                    self?.connectionStats = stats
                    self?.bytesReceived = Int64(stats.bytesReceived)
                    self?.bytesSent = Int64(stats.bytesSent)
                } catch {
                    print("VPNManager: Failed to get stats: \(error)")
                }
            }
        }
    }
    
    private func stopStatsTimer() {
        statsTimer?.invalidate()
        statsTimer = nil
    }

    // MARK: - Development & Testing Methods


    private func validateSingBoxConfiguration(_ configData: Data) async throws {
        print("VPNManager: Validating SingBox configuration")

        // Parse JSON configuration
        guard let configDict = try? JSONSerialization.jsonObject(with: configData) as? [String: Any] else {
            throw VPNError.configurationInvalid
        }

        // Check for required SingBox fields
        guard configDict["inbounds"] != nil else {
            print("VPNManager: Missing 'inbounds' in configuration")
            throw VPNError.configurationInvalid
        }

        guard configDict["outbounds"] != nil else {
            print("VPNManager: Missing 'outbounds' in configuration")
            throw VPNError.configurationInvalid
        }

        print("VPNManager: SingBox configuration validation passed")
    }
}

// MARK: - VPN Statistics
struct VPNStats {
    let connected: Bool
    let uptime: TimeInterval
    let bytesReceived: UInt64
    let bytesSent: UInt64
    
    init(connected: Bool = false, uptime: TimeInterval = 0, bytesReceived: UInt64 = 0, bytesSent: UInt64 = 0) {
        self.connected = connected
        self.uptime = uptime
        self.bytesReceived = bytesReceived
        self.bytesSent = bytesSent
    }
    
    var formattedUptime: String {
        let hours = Int(uptime) / 3600
        let minutes = Int(uptime) % 3600 / 60
        let seconds = Int(uptime) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    var formattedBytesReceived: String {
        return ByteCountFormatter.string(fromByteCount: Int64(bytesReceived), countStyle: .binary)
    }
    
    var formattedBytesSent: String {
        return ByteCountFormatter.string(fromByteCount: Int64(bytesSent), countStyle: .binary)
    }
}

// MARK: - VPN Errors
enum VPNError: Error, LocalizedError {
    case managerNotInitialized
    case connectionFailed(Error)
    case sessionNotAvailable
    case configurationInvalid
    
    var errorDescription: String? {
        switch self {
        case .managerNotInitialized:
            return "VPN manager not initialized"
        case .connectionFailed(let error):
            return "Connection failed: \(error.localizedDescription)"
        case .sessionNotAvailable:
            return "VPN session not available"
        case .configurationInvalid:
            return "Invalid VPN configuration"
        }
    }
}

// MARK: - NEVPNStatus Extension
extension NEVPNStatus {
    var description: String {
        switch self {
        case .invalid:
            return "无效"
        case .disconnected:
            return "已断开"
        case .connecting:
            return "连接中"
        case .connected:
            return "已连接"
        case .reasserting:
            return "重新连接"
        case .disconnecting:
            return "断开中"
        @unknown default:
            return "未知"
        }
    }
}
