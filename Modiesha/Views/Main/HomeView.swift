//
//  HomeView.swift
//  Modiesha
//
//  Created by <PERSON> on 2025/6/27.
//

import SwiftUI
import UniformTypeIdentifiers
import NetworkExtension
import OSLog

// MARK: - Simple ProxyMode for HomeView
enum SimpleProxyMode: String, CaseIterable {
    case rule = "规则"
    case global = "全局"
    case direct = "直连"
}

struct HomeView: View {
    @StateObject private var vpnManager = VPNManager()
    @StateObject private var configManager = ConfigFileManager.shared
    @State private var showingConfigManagement = false
    @State private var selectedMode: SimpleProxyMode = .rule
    @State private var isProxyEnabled = false
    @State private var showingConnectionTest = false

    init() {
        let vpnManager = VPNManager()
        self._vpnManager = StateObject(wrappedValue: vpnManager)
    }
    @State private var isConnecting = false
    @State private var connectionError: String?

    private let logger = Logger(subsystem: "com.modiesha.app", category: "HomeView")

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    headerSection
                    outboundSection
                    configurationSection
                    statisticsSection
                }
                .padding()
            }
            .navigationBarBackButtonHidden(true)
            .background(Color.black.ignoresSafeArea())
        }
        .sheet(isPresented: $showingConfigManagement) {
            ConfigManagementView()
                .environmentObject(configManager)
        }
        .onAppear {
            updateVPNStatus()
            // Debug: Print current config selection state
            print("HomeView onAppear: Selected config = \(configManager.selectedConfig?.name ?? "None")")
            print("HomeView onAppear: Total configs = \(configManager.savedConfigs.count)")
        }
        .alert("Connection Error", isPresented: .constant(connectionError != nil)) {
            Button("OK") {
                connectionError = nil
            }
        } message: {
            if let error = connectionError {
                Text(error)
            }
        }
    }

    // MARK: - VPN Connection Methods
    private func connectVPN() {
        guard let selectedConfig = configManager.selectedConfig else {
            connectionError = "Please select a configuration file first"
            return
        }

        isConnecting = true
        connectionError = nil
        logger.info("Starting VPN connection with config: \(selectedConfig.name)")

        Task {
            do {
                // Validate selected config exists
                guard let selectedConfig = configManager.selectedConfig else {
                    throw NSError(domain: "ConfigError", code: 1, userInfo: [NSLocalizedDescriptionKey: "No configuration file selected"])
                }

                logger.info("Using selected config: \(selectedConfig.name) at path: \(selectedConfig.filePath.path)")

                // Get configuration data from selected config
                guard let configData = try configManager.getSelectedConfigData() else {
                    throw NSError(domain: "ConfigError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Configuration file not found or could not be read"])
                }
                logger.info("Loaded configuration data: \(configData.count) bytes from \(selectedConfig.fileName)")

                // Start VPN connection using VPNManager
                try await vpnManager.connect()

                await MainActor.run {
                    isConnecting = false
                    updateVPNStatus()
                    logger.info("VPN connection established successfully")
                }

            } catch {
                await MainActor.run {
                    isConnecting = false
                    let errorMessage: String
                    if let configError = error as? ConfigError {
                        errorMessage = "Config Error: \(configError.localizedDescription)"
                    } else {
                        errorMessage = "Failed to connect: \(error.localizedDescription)"
                    }
                    connectionError = errorMessage
                    logger.error("VPN connection failed: \(error.localizedDescription)")
                }
            }
        }
    }

    private func disconnectVPN() {
        isConnecting = true

        Task {
            do {
                try await vpnManager.disconnect()

                await MainActor.run {
                    isConnecting = false
                    updateVPNStatus()
                }

            } catch {
                await MainActor.run {
                    isConnecting = false
                    connectionError = "Failed to disconnect: \(error.localizedDescription)"
                }
            }
        }
    }

    // MARK: - Helper Methods
    private func loadConfigurationData(from url: URL) async throws -> Data {
        return try await withCheckedThrowingContinuation { continuation in
            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    // Start accessing security-scoped resource
                    let hasAccess = url.startAccessingSecurityScopedResource()
                    defer {
                        if hasAccess {
                            url.stopAccessingSecurityScopedResource()
                        }
                    }

                    let data = try Data(contentsOf: url)
                    continuation.resume(returning: data)

                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    // MARK: - VPN Status Monitoring

    private func updateVPNStatus() {
        // Update UI based on VPNManager status
        isProxyEnabled = vpnManager.isConnected

        // Monitor connection status changes
        if let error = vpnManager.lastError {
            connectionError = error.localizedDescription
        }
    }

    // MARK: - Debug Methods

    private func debugConfigSelection() {
        print("=== CONFIG SELECTION DEBUG ===")
        print("Selected config: \(configManager.selectedConfig?.name ?? "None")")
        print("Total configs: \(configManager.savedConfigs.count)")

        for (index, config) in configManager.savedConfigs.enumerated() {
            print("Config \(index): \(config.name) - Selected: \(config.isSelected)")
        }

        
        let testResult = configManager.testSelectedConfigAccess()
        print("File access test:")
        print(testResult)
        print("=== END DEBUG ===")
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Modiesha")
                    .foregroundColor(.white)
                    .font(.system(size: 28, weight: .bold))
                Spacer()
                
                Button(action: { showingConfigManagement = true }) {
                    Image(systemName: "gear")
                        .foregroundColor(.white)
                        .font(.system(size: 20))
                        .frame(width: 44, height: 44)
                        .background(Color.gray.opacity(0.3))
                        .cornerRadius(22)
                }
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("代理状态")
                        .foregroundColor(.gray)
                        .font(.system(size: 14))
                    
                    Text(isProxyEnabled ? "已连接" : "未连接")
                        .foregroundColor(isProxyEnabled ? .green : .red)
                        .font(.system(size: 16, weight: .medium))
                }
                
                Spacer()
                
                Button(action: {
                    if isProxyEnabled {
                        disconnectVPN()
                    } else {
                        connectVPN()
                    }
                }) {
                    HStack(spacing: 4) {
                        if isConnecting {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        }
                        Text(isConnecting ? "连接中..." : (isProxyEnabled ? "断开" : "连接"))
                            .foregroundColor(.white)
                            .font(.system(size: 16, weight: .medium))
                    }
                    .frame(width: 80, height: 36)
                    .background(isProxyEnabled ? Color.red : (isConnecting ? Color.orange : Color.green))
                    .cornerRadius(18)
                }
                .disabled(isConnecting || configManager.selectedConfig == nil)
            }
        }

    }
    
    // MARK: - Outbound Section
    private var outboundSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("出站")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
                Spacer()
            }
            
            VStack(spacing: 12) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))
                        .frame(width: 30)
                    
                    Text("规则模式")
                        .foregroundColor(.white)
                        .font(.system(size: 16))
                    
                    Spacer()
                }
                .padding()
                .background(Color.gray.opacity(0.2))
                .cornerRadius(12)
                
                modeSelector
            }
        }
    }
    
    private var modeSelector: some View {
        HStack(spacing: 0) {
            ForEach(SimpleProxyMode.allCases, id: \.self) { mode in
                Button(action: { selectedMode = mode }) {
                    Text(mode.rawValue)
                        .foregroundColor(selectedMode == mode ? .black : .white)
                        .font(.system(size: 14, weight: .medium))
                        .frame(maxWidth: .infinity, minHeight: 36)
                        .background(selectedMode == mode ? Color.white : Color.clear)
                        .cornerRadius(8)
                }
            }
        }
        .padding(4)
        .background(Color.gray.opacity(0.3))
        .cornerRadius(12)
    }
    
    // MARK: - Configuration Section
    private var configurationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("配置文件")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
                Spacer()

                Button(action: { showingConfigManagement = true }) {
                    Text("管理")
                        .foregroundColor(.blue)
                        .font(.system(size: 14))
                }
            }

            // Selected config display
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(configManager.selectedConfig?.name ?? "未选择配置文件")
                        .foregroundColor(.white)
                        .font(.system(size: 16, weight: .medium))

                    if let selectedConfig = configManager.selectedConfig {
                        Text("添加于 \(selectedConfig.formattedDateAdded)")
                            .foregroundColor(.gray)
                            .font(.system(size: 12))
                    } else {
                        Text("点击管理按钮添加配置文件")
                            .foregroundColor(.gray)
                            .font(.system(size: 12))
                    }
                }

                Spacer()

                if configManager.selectedConfig != nil {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .font(.system(size: 20))
                } else {
                    Button("Debug") {
                        debugConfigSelection()
                    }
                    .foregroundColor(.orange)
                    .font(.caption)
                }
            }
            .padding()
            .background(Color.gray.opacity(0.2))
            .cornerRadius(12)
            .onTapGesture {
                showingConfigManagement = true
            }
        }
    }
    
    private func configToggle(title: String, isOn: Binding<Bool>) -> some View {
        HStack {
            Text(title)
                .foregroundColor(.white)
                .font(.system(size: 14))
            
            Spacer()
            
            Toggle("", isOn: isOn)
                .toggleStyle(SwitchToggleStyle(tint: .blue))
                .scaleEffect(0.8)
        }
        .padding()
        .background(Color.gray.opacity(0.2))
        .cornerRadius(12)
    }
    
    // MARK: - Statistics Section
    private var statisticsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("统计")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
                Spacer()
            }
            
            HStack(spacing: 16) {
                statCard(title: "请求", value: "0", color: .blue)
                statCard(title: "上传", value: "0 KB", color: .green)
                statCard(title: "下载", value: "0 KB", color: .orange)
            }
        }
    }
    
    private func statCard(title: String, value: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Text(title)
                .foregroundColor(.gray)
                .font(.system(size: 12))
            
            Text(value)
                .foregroundColor(color)
                .font(.system(size: 16, weight: .medium))
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color.gray.opacity(0.2))
        .cornerRadius(12)
    }
}
