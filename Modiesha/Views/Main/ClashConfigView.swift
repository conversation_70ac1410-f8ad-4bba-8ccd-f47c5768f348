//
//  ClashConfigView.swift
//  Modiesha
//
//  Created by <PERSON> on 2025/6/27.
//

import SwiftUI
import UniformTypeIdentifiers

struct ClashConfigView: View {
    @EnvironmentObject var clashManager: ClashManager
    @Environment(\.dismiss) private var dismiss
    @StateObject private var vpnManager = VPNManager()

    @State private var showingFilePicker = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingDeleteConfirmation = false
    @State private var configToDelete: ClashManagerConfigFile?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header with import button
                ClashHeaderSection()

                // Config list
                ConfigListSection()

                // Status section
                StatusSection()

                // Test section
                TestSection()
            }
            .navigationTitle("Clash 配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("导入") {
                        showingFilePicker = true
                    }
                }
            }
        }
        .fileImporter(
            isPresented: $showingFilePicker,
            allowedContentTypes: [.yaml, .plainText],
            allowsMultipleSelection: false
        ) { result in
            handleFileImport(result)
        }
        .alert("Clash 配置", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        .confirmationDialog(
            "删除配置",
            isPresented: $showingDeleteConfirmation,
            presenting: configToDelete
        ) { config in
            Button("删除", role: .destructive) {
                deleteConfig(config)
            }
            Button("取消", role: .cancel) { }
        } message: { config in
            Text("确定要删除配置 \"\(config.name)\" 吗？")
        }
        .onAppear {
            clashManager.loadConfigFiles()
        }
    }
}

// MARK: - Clash Header Section
struct ClashHeaderSection: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "doc.text.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Clash 配置文件")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("选择或导入 YAML 配置文件")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
            .padding()
            
            Divider()
        }
        .background(Color(UIColor.systemGroupedBackground))
    }
}

// MARK: - Config List Section
struct ConfigListSection: View {
    @EnvironmentObject var clashManager: ClashManager
    @State private var configToDelete: ClashManagerConfigFile?
    @State private var showingDeleteConfirmation = false
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 8) {
                if clashManager.configFiles.isEmpty {
                    EmptyStateView()
                } else {
                    ForEach(clashManager.configFiles) { config in
                        ConfigFileCard(
                            config: config,
                            onSelect: { selectConfig(config) },
                            onDelete: { 
                                configToDelete = config
                                showingDeleteConfirmation = true
                            }
                        )
                    }
                }
            }
            .padding()
        }
    }
    
    private func selectConfig(_ config: ClashManagerConfigFile) {
        do {
            try clashManager.selectConfig(config)
        } catch {
            // Handle error
        }
    }
}

// MARK: - Config File Card
struct ConfigFileCard: View {
    let config: ClashManagerConfigFile
    let onSelect: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(config.name)
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if config.isActive {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                        }
                        
                        Spacer()
                    }
                    
                    Text("修改时间: \(config.lastModified, formatter: dateFormatter)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let clashConfig = config.config {
                        HStack(spacing: 16) {
                            Label("\(clashConfig.port)", systemImage: "network")
                            Label(clashConfig.mode.capitalized, systemImage: "gear")
                            Label("\(clashConfig.proxies.count) 代理", systemImage: "server.rack")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(spacing: 8) {
                    Button(action: onSelect) {
                        Text(config.isActive ? "已选择" : "选择")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(config.isActive ? Color.green : Color.blue)
                            .cornerRadius(8)
                    }
                    .disabled(config.isActive)
                    
                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                }
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }
}

// MARK: - Empty State View
struct EmptyStateView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("暂无配置文件")
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("点击右上角的\"导入\"按钮\n添加 Clash 配置文件")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 40)
    }
}

// MARK: - Status Section
struct StatusSection: View {
    @EnvironmentObject var clashManager: ClashManager
    
    var body: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Clash 状态")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Circle()
                            .fill(statusColor)
                            .frame(width: 8, height: 8)
                        
                        Text(statusText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if clashManager.activeConfig != nil {
                    Button(action: toggleClash) {
                        Text(clashManager.isClashRunning ? "停止" : "启动")
                            .font(.caption)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(clashManager.isClashRunning ? Color.red : Color.green)
                            .cornerRadius(8)
                    }
                }
            }
            .padding()
            .background(Color(UIColor.systemGroupedBackground))
        }
    }
    
    private var statusColor: Color {
        switch clashManager.clashStatus {
        case .stopped:
            return .gray
        case .starting:
            return .orange
        case .running:
            return .green
        case .error:
            return .red
        }
    }
    
    private var statusText: String {
        switch clashManager.clashStatus {
        case .stopped:
            return "已停止"
        case .starting:
            return "启动中..."
        case .running:
            return "运行中"
        case .error:
            return "错误"
        }
    }
    
    private func toggleClash() {
        if clashManager.isClashRunning {
            clashManager.stopClash()
        } else {
            do {
                try clashManager.startClash()
            } catch {
                // Handle error
            }
        }
    }
}

// MARK: - Main View Extension
extension ClashConfigView {
    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            
            do {
                try clashManager.importConfigFile(from: url)
                showAlert("配置文件导入成功")
            } catch {
                showAlert("导入失败: \(error.localizedDescription)")
            }
            
        case .failure(let error):
            showAlert("文件访问失败: \(error.localizedDescription)")
        }
    }
    
    private func deleteConfig(_ config: ClashManagerConfigFile) {
        do {
            try clashManager.deleteConfigFile(config)
            showAlert("配置文件已删除")
        } catch {
            showAlert("删除失败: \(error.localizedDescription)")
        }
    }
    
    private func showAlert(_ message: String) {
        alertMessage = message
        showingAlert = true
    }

    // MARK: - Test Section
    private func TestSection() -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("测试")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
                Spacer()
            }
            .padding(.horizontal)

            VStack(spacing: 12) {
                // VPN Status
                HStack {
                    Image(systemName: "network")
                        .foregroundColor(.blue)
                        .font(.system(size: 20))
                        .frame(width: 30)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("VPN 状态")
                            .foregroundColor(.white)
                            .font(.system(size: 16))

                        Text(vpnManager.isConnected ? "已连接" : "未连接")
                            .foregroundColor(vpnManager.isConnected ? .green : .gray)
                            .font(.system(size: 14))
                    }

                    Spacer()

                    Text(vpnManager.connectionStatus.description)
                        .foregroundColor(.gray)
                        .font(.system(size: 12))
                }
                .padding()
                .background(Color.gray.opacity(0.2))
                .cornerRadius(12)

                // Test Configuration Button
                Button(action: {
                    testSingBoxConfiguration()
                }) {
                    HStack {
                        Image(systemName: "play.circle")
                            .foregroundColor(.white)
                            .font(.system(size: 20))

                        Text("测试 Sing-box 配置")
                            .foregroundColor(.white)
                            .font(.system(size: 16, weight: .medium))

                        Spacer()
                    }
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }

                // Statistics
                if vpnManager.isConnected {
                    VStack(spacing: 8) {
                        HStack {
                            Text("运行时间:")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                            Spacer()
                            Text(vpnManager.connectionStats.formattedUptime)
                                .foregroundColor(.white)
                                .font(.system(size: 14))
                        }

                        HStack {
                            Text("下载:")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                            Spacer()
                            Text(vpnManager.connectionStats.formattedBytesReceived)
                                .foregroundColor(.white)
                                .font(.system(size: 14))
                        }

                        HStack {
                            Text("上传:")
                                .foregroundColor(.gray)
                                .font(.system(size: 14))
                            Spacer()
                            Text(vpnManager.connectionStats.formattedBytesSent)
                                .foregroundColor(.white)
                                .font(.system(size: 14))
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
        }
    }

    private func testSingBoxConfiguration() {
        Task {
            do {
                // Create a sample configuration
                let sampleConfig = SingBoxConfigGenerator.createSampleConfig()

                // Test VPN connection
                try await vpnManager.connect()

                await MainActor.run {
                    alertMessage = "Sing-box 配置测试成功！VPN 连接已建立。"
                    showingAlert = true
                }
            } catch {
                await MainActor.run {
                    alertMessage = "测试失败: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
        }
    }
}

#Preview {
    ClashConfigView()
        .preferredColorScheme(.dark)
}
